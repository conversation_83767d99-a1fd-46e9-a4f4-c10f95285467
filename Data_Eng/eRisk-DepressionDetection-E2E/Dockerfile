FROM apache/airflow:2.9.2-python3.11

COPY requirements.txt /opt/airflow/requirements.txt

USER root
RUN apt-get update && \
    apt-get install -y gcc libpq-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

USER airflow

RUN pip install --upgrade pip setuptools && \
    pip install --no-cache-dir -r /opt/airflow/requirements.txt --constraint "https://raw.githubusercontent.com/apache/airflow/constraints-2.9.2/constraints-3.11.txt"
