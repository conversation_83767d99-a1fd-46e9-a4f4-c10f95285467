a2wsgi==1.10.10
aiologic==0.14.0
aiosqlite==0.21.0
alembic==1.13.1
annotated-types==0.7.0
anyio==4.9.0
apache-airflow==3.0.2
apache-airflow-core==3.0.2
apache-airflow-providers-common-compat==1.7.1
apache-airflow-providers-common-io==1.6.0
apache-airflow-providers-common-sql==1.27.2
apache-airflow-providers-smtp==2.1.0
apache-airflow-providers-standard==1.3.0
apache-airflow-task-sdk==1.0.2
appnope==0.1.3
argcomplete==3.6.2
asgiref==3.8.1
asttokens==2.2.1
attrs==25.3.0
backcall==0.2.0
blinker==1.9.0
cadwyn==5.4.2
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorlog==6.9.0
comm==0.1.2
cron-descriptor==1.4.5
croniter==6.0.0
cryptography==45.0.4
debugpy==1.6.6
decorator==5.1.1
Deprecated==1.2.18
dill==0.4.0
dnspython==2.7.0
email_validator==2.2.0
executing==1.2.0
fastapi==0.115.13
fastapi-cli==0.0.7
Flask==3.1.1
fsspec==2025.5.1
googleapis-common-protos==1.70.0
greenlet==3.2.3
grpcio==1.73.0
gunicorn==23.0.0
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
idna==3.10
importlib_metadata==8.7.0
ipykernel==6.21.3
ipython==8.11.0
itsdangerous==2.2.0
jedi==0.18.2
Jinja2==3.1.6
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
jupyter_client==8.0.3
jupyter_core==5.2.0
lazy-object-proxy==1.11.0
libcst==1.8.2
linkify-it-py==2.0.3
lockfile==0.12.2
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib-inline==0.1.6
mdurl==0.1.2
methodtools==0.4.7
more-itertools==10.7.0
msgspec==0.19.0
nest-asyncio==1.5.6
numpy==2.3.1
opentelemetry-api==1.34.1
opentelemetry-exporter-otlp==1.34.1
opentelemetry-exporter-otlp-proto-common==1.34.1
opentelemetry-exporter-otlp-proto-grpc==1.34.1
opentelemetry-exporter-otlp-proto-http==1.34.1
opentelemetry-proto==1.34.1
opentelemetry-sdk==1.34.1
opentelemetry-semantic-conventions==0.55b1
packaging==25.0
panadas==0.2
parso==0.8.3
pathspec==0.12.1
pendulum==3.1.0
pexpect==4.8.0
pickleshare==0.7.5
pluggy==1.6.0
praw==7.8.1
prawcore==2.4.0
prompt-toolkit==3.0.38
protobuf==5.29.5
psutil==7.0.0
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
Pygments==2.14.0
PyJWT==2.10.1
python-daemon==3.1.2
python-dateutil==2.8.2
python-dotenv==1.1.1
python-multipart==0.0.20
python-slugify==8.0.4
pytz==2025.2
PyYAML==6.0.2
pyzmq==25.0.0
referencing==0.36.2
requests==2.32.4
retryhttp==1.3.3
rich==14.0.0
rich-argparse==1.7.1
rich-toolkit==0.14.7
rpds-py==0.25.1
setproctitle==1.3.6
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
SQLAlchemy==1.4.54
SQLAlchemy-JSONField==1.0.2
SQLAlchemy-Utils==0.41.2
sqlparse==0.5.3
stack-data==0.6.2
starlette==0.46.2
structlog==25.4.0
svcs==25.1.0
tabulate==0.9.0
tenacity==9.1.2
termcolor==3.1.0
text-unidecode==1.3
tornado==6.2
traitlets==5.9.0
typer==0.16.0
types-requests==2.32.4.20250611
typing-inspection==0.4.1
typing_extensions==4.13.2
tzdata==2025.2
uc-micro-py==1.0.3
universal_pathlib==0.2.6
update-checker==0.18.0
urllib3==2.5.0
uuid6==2025.0.0
uvicorn==0.34.3
uvloop==0.21.0
watchfiles==1.1.0
wcwidth==0.2.6
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wirerope==1.0.0
wrapt==1.17.2
zipp==3.23.0
